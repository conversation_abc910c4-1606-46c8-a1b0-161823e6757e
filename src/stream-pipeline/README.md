# Stream Processing Pipeline Framework

A composable, tickable stream processing framework for real-time data processing with zero-copy semantics.

## Overview

The Stream Processing Pipeline Framework provides a foundation for building high-performance, real-time data processing pipelines. It consists of three main components:

1. **Tickable** - Base interface for components that can be polled for processing
2. **StreamLink** - Active components that manage data flow between pipeline stages
3. **StreamNode** - Passive components that process data when called

## Design Principles

- **Zero-copy semantics**: All data transfer uses move semantics (&&) to avoid unnecessary copies
- **Passive nodes**: StreamNode instances only process when their process() method is called
- **Active links**: StreamLink instances manage data flow and may implement custom tick() behavior
- **Ownership transfer**: Rvalue references indicate clear ownership transfer at each stage
- **Composability**: Components can be chained together through link connections

## Components

### Tickable Interface

Base interface for components that need periodic processing:

```cpp
class Tickable {
public:
    virtual ~Tickable() = default;
    virtual bool tick() = 0;  // Returns true if needs more ticks, false if idle
    virtual bool hasPendingWork() const { return false; }  // Optional override
};
```

### StreamLink Template

Template-based interface for connecting stream processing components:

```cpp
template<typename TInput, typename TOutput>
class StreamLink : public Tickable {
public:
    using OutputCallback = std::function<void(TOutput&&)>;
    
    virtual void forward(TInput&& data) = 0;
    virtual void onOutput(OutputCallback callback) = 0;
};
```

### StreamNode Template

Abstract base class for passive stream processing components:

```cpp
template<typename TInput, typename TOutput>
class StreamNode {
public:
    virtual void process(TInput&& input) = 0;
    
    template<typename TLinkOutput>
    void connectInputLink(StreamLink<TLinkOutput, TInput>* link);
    
    template<typename TLinkInput>
    void connectOutputLink(StreamLink<TOutput, TLinkInput>* link);

protected:
    void sendOutput(TOutput&& data);
};
```

## Usage Example

```cpp
#include "stream-pipeline/tickable.h"
#include "stream-pipeline/stream_link.h"
#include "stream-pipeline/stream_node.h"

using namespace SPipeline;

// Example data types
struct InputData { int value; };
struct ProcessedData { int processed_value; };

// Example processing node
class MyProcessor : public StreamNode<InputData, ProcessedData> {
public:
    void process(InputData&& input) override {
        ProcessedData result;
        result.processed_value = input.value * 2;  // Simple processing
        sendOutput(std::move(result));
    }
};

// Example link implementation
class MyLink : public StreamLink<ProcessedData, ProcessedData> {
private:
    OutputCallback outputCallback_;
    
public:
    void forward(ProcessedData&& data) override {
        // Pass through or transform data
        if (outputCallback_) {
            outputCallback_(std::move(data));
        }
    }
    
    void onOutput(OutputCallback callback) override {
        outputCallback_ = std::move(callback);
    }
    
    bool tick() override {
        // Perform any periodic processing
        return false;  // No pending work
    }
};

// Usage
MyProcessor processor;
MyLink link;

// Connect components
processor.connectOutputLink(&link);

// Process data
InputData input{42};
processor.process(std::move(input));
```

## Integration with Existing Project

The framework is designed to integrate seamlessly with the existing BladeRF video project:

- Uses the same header guard style (`#ifndef` instead of `#pragma once`)
- Follows existing template and documentation patterns
- Compatible with existing `SampleType` definitions from `types.h`
- Designed for future extension with video decoders, stream converters, etc.

## Future Extensions

This framework provides the foundation for implementing:

- Video stream decoders
- IQ data processors
- Real-time filters and converters
- Multi-stage processing pipelines
- Hardware-accelerated processing components

## Thread Safety

The framework components are not inherently thread-safe. For multi-threaded usage:

- Use appropriate synchronization mechanisms
- Consider lock-free designs for high-performance scenarios
- Ensure proper ownership transfer across thread boundaries
