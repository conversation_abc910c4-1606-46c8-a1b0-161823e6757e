#pragma once

#include "tickable.h"
#include <functional>

namespace SPipeline {

/**
 * IStreamLinkInput - Interface for input links in the stream processing pipeline
 * Template Parameter:
 * @tparam TInput Input data type that this link receives
 */
template<typename TInput>
class IStreamLinkInput {
  virtual ~IStreamLinkInput() = 0;
  virtual void forward(TInput&& data) = 0; // Forward input data through the link
};

/**
 * IStreamLinkOutput - Interface for output links in the stream processing pipeline
 * Template Parameter:
 * @tparam TOutput Output data type that this link produces
 */
template<typename TOutput>
class IStreamLinkOutput {
  using OutputCallback = std::function<void(TOutput&&)>;

  virtual ~IStreamLinkOutput() = 0;
  virtual void onOutput(OutputCallback callback) = 0; // Function to call with processed output data
};

/**
 * StreamLink - Template-based interface for connecting stream processing components
 */
template<typename TInput, typename TOutput>
class StreamLink : public Tickable, public IStreamLinkInput<TInput>, public IStreamLinkOutput<TOutput> {

};

} // namespace SPipeline
