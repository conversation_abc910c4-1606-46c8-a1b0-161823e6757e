#pragma once

#include "stream_link.h"
#include <functional>

namespace SPipeline {

/**
 * StreamNode - Abstract base class for passive stream processing components
 */
template<typename TInput, typename TOutput>
class StreamNode: public Tickable {
public:
    ~StreamNode() override = 0;

    /**
     * Process input data and produce output
     */
    virtual void process(TInput&& input) = 0;
    
    /**
     * Connect this node to an input link
     */
    void connectInputLink(IStreamLinkOutput<TInput>* link) {
        if (link) {
            link->onOutput([this](TInput&& data) {
                this->process(std::move(data));
            });
        }
    }
    
    /**
     * Connect this node to an output link
     */
    template<typename TLinkInput>
    void connectOutputLink(IStreamLinkInput<TOutput>* link) {
        outputCallback_ = [link](TOutput&& data) {
            if (link) {
                link->forward(std::move(data));
            }
        };
    }

protected:
    /**
     * Send processed data to the connected output link
     */
    void sendOutput(TOutput&& data) {
        if (outputCallback_) {
            outputCallback_(std::move(data));
        }
    }

private:
    std::function<void(TOutput &&)> outputCallback_;
};

} // namespace SPipeline
